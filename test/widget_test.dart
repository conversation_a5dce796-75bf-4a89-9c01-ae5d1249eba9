import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:ugly_avatar/main.dart';
import 'package:ugly_avatar/complete_face_generator.dart';
import 'package:ugly_avatar/widgets/face/face_shape.dart';
import 'package:ugly_avatar/widgets/eye/eye_shape.dart';
import 'package:ugly_avatar/widgets/mouth/mouth_shape.dart';

void main() {
  group('CompleteFaceGenerator Tests', () {
    testWidgets('CompleteFaceGeneratorWidget renders without flickering', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: CompleteFaceGeneratorWidget(size: 200),
        ),
      ));

      // Verify that the widget is rendered
      expect(find.byType(CompleteFaceGeneratorWidget), findsOneWidget);
      expect(find.byType(CustomPaint), findsOneWidget);
      
      // Verify buttons are present
      expect(find.text('ANOTHER'), findsOneWidget);
      expect(find.text('DOWNLOAD'), findsOneWidget);
    });

    testWidgets('Face generation creates consistent data', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: CompleteFaceGeneratorWidget(size: 200),
        ),
      ));

      // Find the widget state
      final CompleteFaceGeneratorWidget widget = tester.widget(find.byType(CompleteFaceGeneratorWidget));
      expect(widget.size, equals(200));

      // Pump a few frames to ensure no flickering occurs
      await tester.pump();
      await tester.pump(const Duration(milliseconds: 100));
      await tester.pump(const Duration(milliseconds: 100));
      
      // The test passes if no exceptions are thrown during rendering
    });

    testWidgets('ANOTHER button generates new face', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: CompleteFaceGeneratorWidget(size: 200),
        ),
      ));

      // Tap the ANOTHER button
      await tester.tap(find.text('ANOTHER'));
      await tester.pump();

      // Verify the widget still renders correctly after generating new face
      expect(find.byType(CompleteFaceGeneratorWidget), findsOneWidget);
      expect(find.byType(CustomPaint), findsOneWidget);
    });

    test('RandomRenderData contains required fields', () {
      const randomData = RandomRenderData(
        hairStrokeWidths: [1.0, 2.0, 3.0],
        pupilRadii: [[1.0, 2.0], [3.0, 4.0]],
        pupilOffsets: [[Point2D(0, 0)], [Point2D(1, 1)]],
        noseRadii: [1.0, 2.0],
        noseOffsets: [Point2D(0, 0), Point2D(1, 1)],
        mouthStrokeWidth: 2.5,
      );

      expect(randomData.hairStrokeWidths.length, equals(3));
      expect(randomData.pupilRadii.length, equals(2));
      expect(randomData.pupilOffsets.length, equals(2));
      expect(randomData.noseRadii.length, equals(2));
      expect(randomData.noseOffsets.length, equals(2));
      expect(randomData.mouthStrokeWidth, equals(2.5));
    });

    test('Face generation produces stable rendering data', () {
      // Test that the face generation creates consistent random data
      // This ensures no flickering during rendering
      const randomData1 = RandomRenderData(
        hairStrokeWidths: [1.5, 2.0],
        pupilRadii: [[3.0, 4.0], [3.5, 4.5]],
        pupilOffsets: [[Point2D(1, 1), Point2D(2, 2)], [Point2D(-1, -1), Point2D(-2, -2)]],
        noseRadii: [1.5, 2.0],
        noseOffsets: [Point2D(0.5, 0.5), Point2D(-0.5, -0.5)],
        mouthStrokeWidth: 3.0,
      );

      const randomData2 = RandomRenderData(
        hairStrokeWidths: [1.5, 2.0],
        pupilRadii: [[3.0, 4.0], [3.5, 4.5]],
        pupilOffsets: [[Point2D(1, 1), Point2D(2, 2)], [Point2D(-1, -1), Point2D(-2, -2)]],
        noseRadii: [1.5, 2.0],
        noseOffsets: [Point2D(0.5, 0.5), Point2D(-0.5, -0.5)],
        mouthStrokeWidth: 3.0,
      );

      // Verify that identical random data produces identical results
      expect(randomData1.hairStrokeWidths, equals(randomData2.hairStrokeWidths));
      expect(randomData1.pupilRadii, equals(randomData2.pupilRadii));
      expect(randomData1.mouthStrokeWidth, equals(randomData2.mouthStrokeWidth));
    });
  });
}
