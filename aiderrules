{

  "project_type": "flutter",

  "packages": [

    "go_router",

    "flutter_riverpod",

    "json_annotation",

    "flutter_smart_dialog",

    "flutter_screenutil"

  ],

  "guidelines": [

    "Emphasize clean architecture with separation of concerns.",

    "Implement the repository pattern for data management.",

    "Utilize Riverpod for state management.",

    "Focus on creating reusable and modular components.",

    "don't use asset image directly, use asset image like this way: Assets.images.unitSyncMainBg.image().",

    "如果我用中文和你交流时, 你也要用中文回复我",

  ],
 
}