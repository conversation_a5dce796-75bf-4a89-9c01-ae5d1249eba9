import 'dart:math';

/// 实用工具类，用于生成指定范围内的随机值
class RandomUtils {
  static final Random _random = Random();
  
  /// 返回[min, max]范围内的随机双精度值
  static double randomFromInterval(double min, double max) {
    return _random.nextDouble() * (max - min) + min;
  }
}

/// 表示一个二维点
class Point2D {
  final double x;
  final double y;
  
  const Point2D(this.x, this.y);
  
  Point2D operator +(Point2D other) => Point2D(x + other.x, y + other.y);
  Point2D operator -(Point2D other) => Point2D(x - other.x, y - other.y);
  Point2D operator *(double scalar) => Point2D(x * scalar, y * scalar);
  
  @override
  String toString() => 'Point2D($x, $y)';
}

/// 三次贝塞尔曲线计算
class BezierUtils {
  /// 计算三次贝塞尔曲线上的点
  static Point2D cubicBezier(Point2D p0, Point2D p1, Point2D p2, Point2D p3, double t) {
    final double oneMinusT = 1 - t;
    final double oneMinusTSquared = oneMinusT * oneMinusT;
    final double oneMinusTCubed = oneMinusTSquared * oneMinusT;
    final double tSquared = t * t;
    final double tCubed = tSquared * t;
    
    final double x = oneMinusTCubed * p0.x +
                    3 * oneMinusTSquared * t * p1.x +
                    3 * oneMinusT * tSquared * p2.x +
                    tCubed * p3.x;
                    
    final double y = oneMinusTCubed * p0.y +
                    3 * oneMinusTSquared * t * p1.y +
                    3 * oneMinusT * tSquared * p2.y +
                    tCubed * p3.y;
    
    return Point2D(x, y);
  }
}

/// 用于生成眼睛形状的参数
class EyeParameters {
  final double heightUpper; // 上眼睑高度
  final double heightLower; // 下眼睑高度
  final double p0UpperRandX; // 上眼睑起始点 X 坐标随机偏移量
  final double p3UpperRandX; // 上眼睑结束点 X 坐标随机偏移量
  final double p0UpperRandY; // 上眼睑起始点 Y 坐标随机偏移量
  final double p3UpperRandY; // 上眼睑结束点 Y 坐标随机偏移量
  final double offsetUpperLeftRandY; // 上眼睑左侧控制点 Y 坐标随机偏移量
  final double offsetUpperRightRandY; // 上眼睑右侧控制点 Y 坐标随机偏移量
  final double eyeTrueWidth; // 眼睛的实际宽度
  final double offsetUpperLeftX; // 上眼睑左侧控制点 X 坐标偏移量
  final double offsetUpperRightX; // 上眼睑右侧控制点 X 坐标偏移量
  final double offsetUpperLeftY; // 上眼睑左侧控制点 Y 坐标偏移量
  final double offsetUpperRightY; // 上眼睑右侧控制点 Y 坐标偏移量
  final double offsetLowerLeftX; // 下眼睑左侧控制点 X 坐标偏移量
  final double offsetLowerRightX; // 下眼睑右侧控制点 X 坐标偏移量
  final double offsetLowerLeftY; // 下眼睑左侧控制点 Y 坐标偏移量
  final double offsetLowerRightY; // 下眼睑右侧控制点 Y 坐标偏移量
  final double leftConverge0;
  final double rightConverge0;
  final double leftConverge1;
  final double rightConverge1;
  
  const EyeParameters({
    required this.heightUpper,
    required this.heightLower,
    required this.p0UpperRandX,
    required this.p3UpperRandX,
    required this.p0UpperRandY,
    required this.p3UpperRandY,
    required this.offsetUpperLeftRandY,
    required this.offsetUpperRightRandY,
    required this.eyeTrueWidth,
    required this.offsetUpperLeftX,
    required this.offsetUpperRightX,
    required this.offsetUpperLeftY,
    required this.offsetUpperRightY,
    required this.offsetLowerLeftX,
    required this.offsetLowerRightX,
    required this.offsetLowerLeftY,
    required this.offsetLowerRightY,
    required this.leftConverge0,
    required this.rightConverge0,
    required this.leftConverge1,
    required this.rightConverge1,
  });
  
  /// 创建一个具有修改值的副本，用于实现不对称效果
  EyeParameters copyWithVariation() {
    return EyeParameters(
      heightUpper: heightUpper + RandomUtils.randomFromInterval(-heightUpper / 2.0, heightUpper / 2.0),
      heightLower: heightLower + RandomUtils.randomFromInterval(-heightLower / 2.0, heightLower / 2.0),
      p0UpperRandX: p0UpperRandX + RandomUtils.randomFromInterval(-p0UpperRandX / 2.0, p0UpperRandX / 2.0),
      p3UpperRandX: p3UpperRandX + RandomUtils.randomFromInterval(-p3UpperRandX / 2.0, p3UpperRandX / 2.0),
      p0UpperRandY: p0UpperRandY + RandomUtils.randomFromInterval(-p0UpperRandY / 2.0, p0UpperRandY / 2.0),
      p3UpperRandY: p3UpperRandY + RandomUtils.randomFromInterval(-p3UpperRandY / 2.0, p3UpperRandY / 2.0),
      offsetUpperLeftRandY: offsetUpperLeftRandY + RandomUtils.randomFromInterval(-offsetUpperLeftRandY / 2.0, offsetUpperLeftRandY / 2.0),
      offsetUpperRightRandY: offsetUpperRightRandY + RandomUtils.randomFromInterval(-offsetUpperRightRandY / 2.0, offsetUpperRightRandY / 2.0),
      eyeTrueWidth: eyeTrueWidth + RandomUtils.randomFromInterval(-eyeTrueWidth / 2.0, eyeTrueWidth / 2.0),
      offsetUpperLeftX: offsetUpperLeftX + RandomUtils.randomFromInterval(-offsetUpperLeftX / 2.0, offsetUpperLeftX / 2.0),
      offsetUpperRightX: offsetUpperRightX + RandomUtils.randomFromInterval(-offsetUpperRightX / 2.0, offsetUpperRightX / 2.0),
      offsetUpperLeftY: offsetUpperLeftY + RandomUtils.randomFromInterval(-offsetUpperLeftY / 2.0, offsetUpperLeftY / 2.0),
      offsetUpperRightY: offsetUpperRightY + RandomUtils.randomFromInterval(-offsetUpperRightY / 2.0, offsetUpperRightY / 2.0),
      offsetLowerLeftX: offsetLowerLeftX + RandomUtils.randomFromInterval(-offsetLowerLeftX / 2.0, offsetLowerLeftX / 2.0),
      offsetLowerRightX: offsetLowerRightX + RandomUtils.randomFromInterval(-offsetLowerRightX / 2.0, offsetLowerRightX / 2.0),
      offsetLowerLeftY: offsetLowerLeftY + RandomUtils.randomFromInterval(-offsetLowerLeftY / 2.0, offsetLowerLeftY / 2.0),
      offsetLowerRightY: offsetLowerRightY + RandomUtils.randomFromInterval(-offsetLowerRightY / 2.0, offsetLowerRightY / 2.0),
      leftConverge0: leftConverge0 + RandomUtils.randomFromInterval(-leftConverge0 / 2.0, leftConverge0 / 2.0),
      rightConverge0: rightConverge0 + RandomUtils.randomFromInterval(-rightConverge0 / 2.0, rightConverge0 / 2.0),
      leftConverge1: leftConverge1 + RandomUtils.randomFromInterval(-leftConverge1 / 2.0, leftConverge1 / 2.0),
      rightConverge1: rightConverge1 + RandomUtils.randomFromInterval(-rightConverge1 / 2.0, rightConverge1 / 2.0),
    );
  }
}

/// 包含眼睛形状的生成点
class EyePoints {
  final List<Point2D> upper; // 上眼睑点
  final List<Point2D> lower; // 下眼睑点
  final Point2D center; // 眼睛中心点
  
  const EyePoints({
    required this.upper,
    required this.lower,
    required this.center,
  });
}

/// 包含左右眼的点
class BothEyePoints {
  final EyePoints left; // 左眼点
  final EyePoints right; // 右眼点

  const BothEyePoints({
    required this.left,
    required this.right,
  });
}

/// 用于生成眼睛形状的主要类
class EyeShapeGenerator {
  static EyeParameters generateEyeParameters(double width) {
    // Increased minHeight and random range to prevent eyes from becoming too narrow
    // Original: final double minHeight = width / 2.5;
    // New: Increased minHeight to make eyes generally taller
    final double minHeight = width / 1; 
    // Original: final double heightUpper = minHeight + Random().nextDouble() * (width / 2.0);
    // New: Adjusted random range for heightUpper and heightLower
    final double heightUpper = minHeight + Random().nextDouble() * (width / 1.8); 
    final double heightLower = minHeight + Random().nextDouble() * (width / 1.8); 
    final double p0UpperRandX = Random().nextDouble() * 0.4 - 0.2;
    final double p3UpperRandX = Random().nextDouble() * 0.4 - 0.2;
    final double p0UpperRandY = Random().nextDouble() * 0.4 - 0.2;
    final double p3UpperRandY = Random().nextDouble() * 0.4 - 0.2;
    final double offsetUpperLeftRandY = Random().nextDouble();
    final double offsetUpperRightRandY = Random().nextDouble();

    final Point2D p0Upper = Point2D(-width / 2 + p0UpperRandX * width / 16, p0UpperRandY * heightUpper / 16);
    final Point2D p3Upper = Point2D(width / 2 + p3UpperRandX * width / 16, p3UpperRandY * heightUpper / 16);
    final double eyeTrueWidth = p3Upper.x - p0Upper.x;

    final double offsetUpperLeftX = RandomUtils.randomFromInterval(-eyeTrueWidth / 12.0, eyeTrueWidth / 4.0); // Upper eyelid control point offset to create asymmetry
    final double offsetUpperRightX = RandomUtils.randomFromInterval(-eyeTrueWidth / 12.0, eyeTrueWidth / 4.0); // Upper eyelid control point offset to create asymmetry
    final double offsetUpperLeftY = offsetUpperLeftRandY * heightUpper / 3; // Upper eyelid control point offset to create asymmetry
    final double offsetUpperRightY = offsetUpperRightRandY * heightUpper / 3; // Upper eyelid control point offset to create asymmetry
    final double offsetLowerLeftX = RandomUtils.randomFromInterval(offsetUpperLeftX, eyeTrueWidth / 4.0); // Lower eyelid control point offset
    final double offsetLowerRightX = RandomUtils.randomFromInterval(offsetUpperRightX, eyeTrueWidth / 4.0); // Upper eyelid control point offset to create asymmetry
    final double offsetLowerLeftY = RandomUtils.randomFromInterval(-offsetUpperLeftY + 1, heightLower / 3); // Upper eyelid control point offset to create asymmetry
    final double offsetLowerRightY = RandomUtils.randomFromInterval(-offsetUpperRightY + 1, heightLower / 3); // Upper eyelid control point offset to create asymmetry

    // Generate points for the Bezier curves
    final double leftConverge0 = Random().nextDouble();
    final double rightConverge0 = Random().nextDouble();
    final double leftConverge1 = Random().nextDouble();
    final double rightConverge1 = Random().nextDouble();

    return EyeParameters(
      heightUpper: heightUpper,
      heightLower: heightLower,
      p0UpperRandX: p0UpperRandX,
      p3UpperRandX: p3UpperRandX,
      p0UpperRandY: p0UpperRandY,
      p3UpperRandY: p3UpperRandY,
      offsetUpperLeftRandY: offsetUpperLeftRandY,
      offsetUpperRightRandY: offsetUpperRightRandY,
      eyeTrueWidth: eyeTrueWidth,
      offsetUpperLeftX: offsetUpperLeftX,
      offsetUpperRightX: offsetUpperRightX,
      offsetUpperLeftY: offsetUpperLeftY,
      offsetUpperRightY: offsetUpperRightY,
      offsetLowerLeftX: offsetLowerLeftX,
      offsetLowerRightX: offsetLowerRightX,
      offsetLowerLeftY: offsetLowerLeftY,
      offsetLowerRightY: offsetLowerRightY,
      leftConverge0: leftConverge0,
      rightConverge0: rightConverge0,
      leftConverge1: leftConverge1,
      rightConverge1: rightConverge1,
    );
  }

  static EyePoints generateEyePoints(EyeParameters rands, {double width = 50}) {
    final Point2D p0Upper = Point2D(-width / 2 + rands.p0UpperRandX * width / 16, rands.p0UpperRandY * rands.heightUpper / 16);
    final Point2D p3Upper = Point2D(width / 2 + rands.p3UpperRandX * width / 16, rands.p3UpperRandY * rands.heightUpper / 16);
    final Point2D p0Lower = p0Upper; // Starting at the same point as the upper eyelid
    final Point2D p3Lower = p3Upper; // Ending at the same point as the upper eyelid

    // Upper eyelid control points
    final Point2D p1Upper = Point2D(p0Upper.x + rands.offsetUpperLeftX, p0Upper.y + rands.offsetUpperLeftY); // First control point
    final Point2D p2Upper = Point2D(p3Upper.x - rands.offsetUpperRightX, p3Upper.y + rands.offsetUpperRightY); // Second control point

    // Lower eyelid control points
    final Point2D p1Lower = Point2D(p0Lower.x + rands.offsetLowerLeftX, p0Lower.y - rands.offsetLowerLeftY); // First control point
    final Point2D p2Lower = Point2D(p3Lower.x - rands.offsetLowerRightX, p3Lower.y - rands.offsetLowerRightY); // Second control point

    // Generate the points for the upper eyelid
    List<Point2D> upperEyelidPoints = [];
    List<Point2D> upperEyelidPointsLeftControl = [];
    List<Point2D> upperEyelidPointsRightControl = [];

    final Point2D upperEyelidLeftControlPoint = Point2D(
      p0Upper.x * (1 - rands.leftConverge0) + p1Lower.x * rands.leftConverge0,
      p0Upper.y * (1 - rands.leftConverge0) + p1Lower.y * rands.leftConverge0
    );
    final Point2D upperEyelidRightControlPoint = Point2D(
      p3Upper.x * (1 - rands.rightConverge0) + p2Lower.x * rands.rightConverge0,
      p3Upper.y * (1 - rands.rightConverge0) + p2Lower.y * rands.rightConverge0
    );

    for (int t = 0; t < 100; t++) {
      upperEyelidPoints.add(BezierUtils.cubicBezier(p0Upper, p1Upper, p2Upper, p3Upper, t / 100));
      upperEyelidPointsLeftControl.add(BezierUtils.cubicBezier(upperEyelidLeftControlPoint, p0Upper, p1Upper, p2Upper, t / 100));
      upperEyelidPointsRightControl.add(BezierUtils.cubicBezier(p1Upper, p2Upper, p3Upper, upperEyelidRightControlPoint, t / 100));
    }

    for (int i = 0; i < 75; i++) {
      final double weight = pow((75.0 - i) / 75.0, 2).toDouble();
      upperEyelidPoints[i] = Point2D(
        upperEyelidPoints[i].x * (1 - weight) + upperEyelidPointsLeftControl[i + 25].x * weight,
        upperEyelidPoints[i].y * (1 - weight) + upperEyelidPointsLeftControl[i + 25].y * weight
      );
      upperEyelidPoints[i + 25] = Point2D(
        upperEyelidPoints[i + 25].x * weight + upperEyelidPointsRightControl[i].x * (1 - weight),
        upperEyelidPoints[i + 25].y * weight + upperEyelidPointsRightControl[i].y * (1 - weight)
      );
    }

    // Generate the points for the lower eyelid
    List<Point2D> lowerEyelidPoints = [];
    List<Point2D> lowerEyelidPointsLeftControl = [];
    List<Point2D> lowerEyelidPointsRightControl = [];

    final Point2D lowerEyelidLeftControlPoint = Point2D(
      p0Lower.x * (1 - rands.leftConverge0) + p1Upper.x * rands.leftConverge0,
      p0Lower.y * (1 - rands.leftConverge0) + p1Upper.y * rands.leftConverge0
    );
    final Point2D lowerEyelidRightControlPoint = Point2D(
      p3Lower.x * (1 - rands.rightConverge1) + p2Upper.x * rands.rightConverge1,
      p3Lower.y * (1 - rands.rightConverge1) + p2Upper.y * rands.rightConverge1
    );

    for (int t = 0; t < 100; t++) {
      lowerEyelidPoints.add(BezierUtils.cubicBezier(p0Lower, p1Lower, p2Lower, p3Lower, t / 100));
      lowerEyelidPointsLeftControl.add(BezierUtils.cubicBezier(lowerEyelidLeftControlPoint, p0Lower, p1Lower, p2Lower, t / 100));
      lowerEyelidPointsRightControl.add(BezierUtils.cubicBezier(p1Lower, p2Lower, p3Lower, lowerEyelidRightControlPoint, t / 100));
    }

    for (int i = 0; i < 75; i++) {
      final double weight = pow((75.0 - i) / 75.0, 2).toDouble();
      lowerEyelidPoints[i] = Point2D(
        lowerEyelidPoints[i].x * (1 - weight) + lowerEyelidPointsLeftControl[i + 25].x * weight,
        lowerEyelidPoints[i].y * (1 - weight) + lowerEyelidPointsLeftControl[i + 25].y * weight
      );
      lowerEyelidPoints[i + 25] = Point2D(
        lowerEyelidPoints[i + 25].x * weight + lowerEyelidPointsRightControl[i].x * (1 - weight),
        lowerEyelidPoints[i + 25].y * weight + lowerEyelidPointsRightControl[i].y * (1 - weight)
      );
    }

    // Flip Y coordinates (in SVG, bottom is y+ and top is y-)
    for (int i = 0; i < 100; i++) {
      lowerEyelidPoints[i] = Point2D(lowerEyelidPoints[i].x, -lowerEyelidPoints[i].y);
      upperEyelidPoints[i] = Point2D(upperEyelidPoints[i].x, -upperEyelidPoints[i].y);
    }

    // Calculate eye center
    final Point2D eyeCenter = Point2D(
      upperEyelidPoints[50].x / 2.0 + lowerEyelidPoints[50].x / 2.0,
      upperEyelidPoints[50].y / 2.0 + lowerEyelidPoints[50].y / 2.0
    );

    // Translate to center
    for (int i = 0; i < 100; i++) {
      lowerEyelidPoints[i] = lowerEyelidPoints[i] - eyeCenter;
      upperEyelidPoints[i] = upperEyelidPoints[i] - eyeCenter;
    }

    return EyePoints(
      upper: upperEyelidPoints,
      lower: lowerEyelidPoints,
      center: const Point2D(0, 0),
    );
  }

  static BothEyePoints generateBothEyes({double width = 50}) {
    final EyeParameters randsLeft = generateEyeParameters(width);
    final EyeParameters randsRight = randsLeft.copyWithVariation();

    EyePoints leftEye = generateEyePoints(randsLeft, width: width);
    final EyePoints rightEye = generateEyePoints(randsRight, width: width);

    // Mirror the left eye by negating x coordinates
    final List<Point2D> leftUpper = leftEye.upper.map((point) => Point2D(-point.x, point.y)).toList();
    final List<Point2D> leftLower = leftEye.lower.map((point) => Point2D(-point.x, point.y)).toList();

    leftEye = EyePoints(
      upper: leftUpper,
      lower: leftLower,
      center: Point2D(-leftEye.center.x, leftEye.center.y),
    );

    return BothEyePoints(
      left: leftEye,
      right: rightEye,
    );
  }
}
