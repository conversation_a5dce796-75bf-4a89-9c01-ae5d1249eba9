import 'dart:math';

/// 实用工具类，用于生成指定范围内的随机值
class RandomUtils {
  static final Random _random = Random();
  
  /// 返回[min, max]范围内的随机双精度值
  static double randomFromInterval(double min, double max) {
    return _random.nextDouble() * (max - min) + min;
  }
}

/// 表示一个二维点
class Point2D {
  final double x;
  final double y;
  
  const Point2D(this.x, this.y);
  
  Point2D operator +(Point2D other) => Point2D(x + other.x, y + other.y);
  Point2D operator -(Point2D other) => Point2D(x - other.x, y - other.y);
  Point2D operator *(double scalar) => Point2D(x * scalar, y * scalar);
  
  @override
  String toString() => 'Point2D($x, $y)';
}

/// 三次贝塞尔曲线计算
class BezierUtils {
  /// 计算三次贝塞尔曲线上的点
  static Point2D cubicBezier(Point2D p0, Point2D p1, Point2D p2, Point2D p3, double t) {
    final double oneMinusT = 1 - t;
    final double oneMinusTSquared = oneMinusT * oneMinusT;
    final double oneMinusTCubed = oneMinusTSquared * oneMinusT;
    final double tSquared = t * t;
    final double tCubed = tSquared * t;
    
    final double x = oneMinusTCubed * p0.x +
                    3 * oneMinusTSquared * t * p1.x +
                    3 * oneMinusT * tSquared * p2.x +
                    tCubed * p3.x;
                    
    final double y = oneMinusTCubed * p0.y +
                    3 * oneMinusTSquared * t * p1.y +
                    3 * oneMinusT * tSquared * p2.y +
                    tCubed * p3.y;
    
    return Point2D(x, y);
  }
}

/// 用于生成眼睛形状的参数
class EyeParameters {
  final double heightUpper; // 上眼睑高度
  final double heightLower; // 下眼睑高度
  final double p0UpperRandX; // 上眼睑起始点 X 坐标随机偏移量
  final double p3UpperRandX; // 上眼睑结束点 X 坐标随机偏移量
  final double p0UpperRandY; // 上眼睑起始点 Y 坐标随机偏移量
  final double p3UpperRandY; // 上眼睑结束点 Y 坐标随机偏移量
  final double offsetUpperLeftRandY; // 上眼睑左侧控制点 Y 坐标随机偏移量
  final double offsetUpperRightRandY; // 上眼睑右侧控制点 Y 坐标随机偏移量
  final double eyeTrueWidth; // 眼睛的实际宽度
  final double offsetUpperLeftX; // 上眼睑左侧控制点 X 坐标偏移量
  final double offsetUpperRightX; // 上眼睑右侧控制点 X 坐标偏移量
  final double offsetUpperLeftY; // 上眼睑左侧控制点 Y 坐标偏移量
  final double offsetUpperRightY; // 上眼睑右侧控制点 Y 坐标偏移量
  final double offsetLowerLeftX; // 下眼睑左侧控制点 X 坐标偏移量
  final double offsetLowerRightX; // 下眼睑右侧控制点 X 坐标偏移量
  final double offsetLowerLeftY; // 下眼睑左侧控制点 Y 坐标偏移量
  final double offsetLowerRightY; // 下眼睑右侧控制点 Y 坐标偏移量
  final double leftConverge0; // 左侧收敛参数0，用于控制眼睑形状的混合
  final double rightConverge0; // 右侧收敛参数0，用于控制眼睑形状的混合
  final double leftConverge1; // 左侧收敛参数1，用于控制眼睑形状的混合
  final double rightConverge1; // 右侧收敛参数1，用于控制眼睑形状的混合
  
  const EyeParameters({
    required this.heightUpper,
    required this.heightLower,
    required this.p0UpperRandX,
    required this.p3UpperRandX,
    required this.p0UpperRandY,
    required this.p3UpperRandY,
    required this.offsetUpperLeftRandY,
    required this.offsetUpperRightRandY,
    required this.eyeTrueWidth,
    required this.offsetUpperLeftX,
    required this.offsetUpperRightX,
    required this.offsetUpperLeftY,
    required this.offsetUpperRightY,
    required this.offsetLowerLeftX,
    required this.offsetLowerRightX,
    required this.offsetLowerLeftY,
    required this.offsetLowerRightY,
    required this.leftConverge0,
    required this.rightConverge0,
    required this.leftConverge1,
    required this.rightConverge1,
  });
  
  /// 创建一个具有修改值的副本，用于实现不对称效果
  EyeParameters copyWithVariation() {
    return EyeParameters(
      heightUpper: heightUpper + RandomUtils.randomFromInterval(-heightUpper / 2.0, heightUpper / 2.0),
      heightLower: heightLower + RandomUtils.randomFromInterval(-heightLower / 2.0, heightLower / 2.0),
      p0UpperRandX: p0UpperRandX + RandomUtils.randomFromInterval(-p0UpperRandX / 2.0, p0UpperRandX / 2.0),
      p3UpperRandX: p3UpperRandX + RandomUtils.randomFromInterval(-p3UpperRandX / 2.0, p3UpperRandX / 2.0),
      p0UpperRandY: p0UpperRandY + RandomUtils.randomFromInterval(-p0UpperRandY / 2.0, p0UpperRandY / 2.0),
      p3UpperRandY: p3UpperRandY + RandomUtils.randomFromInterval(-p3UpperRandY / 2.0, p3UpperRandY / 2.0),
      offsetUpperLeftRandY: offsetUpperLeftRandY + RandomUtils.randomFromInterval(-offsetUpperLeftRandY / 2.0, offsetUpperLeftRandY / 2.0),
      offsetUpperRightRandY: offsetUpperRightRandY + RandomUtils.randomFromInterval(-offsetUpperRightRandY / 2.0, offsetUpperRightRandY / 2.0),
      eyeTrueWidth: eyeTrueWidth + RandomUtils.randomFromInterval(-eyeTrueWidth / 2.0, eyeTrueWidth / 2.0),
      offsetUpperLeftX: offsetUpperLeftX + RandomUtils.randomFromInterval(-offsetUpperLeftX / 2.0, offsetUpperLeftX / 2.0),
      offsetUpperRightX: offsetUpperRightX + RandomUtils.randomFromInterval(-offsetUpperRightX / 2.0, offsetUpperRightX / 2.0),
      offsetUpperLeftY: offsetUpperLeftY + RandomUtils.randomFromInterval(-offsetUpperLeftY / 2.0, offsetUpperLeftY / 2.0),
      offsetUpperRightY: offsetUpperRightY + RandomUtils.randomFromInterval(-offsetUpperRightY / 2.0, offsetUpperRightY / 2.0),
      offsetLowerLeftX: offsetLowerLeftX + RandomUtils.randomFromInterval(-offsetLowerLeftX / 2.0, offsetLowerLeftX / 2.0),
      offsetLowerRightX: offsetLowerRightX + RandomUtils.randomFromInterval(-offsetLowerRightX / 2.0, offsetLowerRightX / 2.0),
      offsetLowerLeftY: offsetLowerLeftY + RandomUtils.randomFromInterval(-offsetLowerLeftY / 2.0, offsetLowerLeftY / 2.0),
      offsetLowerRightY: offsetLowerRightY + RandomUtils.randomFromInterval(-offsetLowerRightY / 2.0, offsetLowerRightY / 2.0),
      leftConverge0: leftConverge0 + RandomUtils.randomFromInterval(-leftConverge0 / 2.0, leftConverge0 / 2.0),
      rightConverge0: rightConverge0 + RandomUtils.randomFromInterval(-rightConverge0 / 2.0, rightConverge0 / 2.0),
      leftConverge1: leftConverge1 + RandomUtils.randomFromInterval(-leftConverge1 / 2.0, leftConverge1 / 2.0),
      rightConverge1: rightConverge1 + RandomUtils.randomFromInterval(-rightConverge1 / 2.0, rightConverge1 / 2.0),
    );
  }
}

/// 包含眼睛形状的生成点
class EyePoints {
  final List<Point2D> upper; // 上眼睑点
  final List<Point2D> lower; // 下眼睑点
  final Point2D center; // 眼睛中心点
  
  const EyePoints({
    required this.upper,
    required this.lower,
    required this.center,
  });

  @override
  String toString() {
    return 'EyePoints(upper: ${upper} , lower: ${lower} , center: ${center} )';
  }
}

/// 包含左右眼的点
class BothEyePoints {
  final EyePoints left; // 左眼点
  final EyePoints right; // 右眼点

  const BothEyePoints({
    required this.left,
    required this.right,
  });

  @override
  String toString() {
    return 'BothEyePoints(left: ${left}  , right: ${right}  )';
  }
}

/// 用于生成眼睛形状的主要类
class EyeShapeGenerator {
  /// 生成眼睛参数
  /// [width] 眼睛的基础宽度
  static EyeParameters generateEyeParameters(double width) {
    // 增加最小高度和随机范围，防止眼睛变得太窄
    // 原始值: final double minHeight = width / 2.5;
    // 新值: 增加最小高度使眼睛通常更高
    final double minHeight = width / 2;
    // 原始值: final double heightUpper = minHeight + Random().nextDouble() * (width / 2.0);
    // 新值: 调整上下眼睑的随机范围
    final double heightUpper = minHeight + Random().nextDouble() * (width / 1.8);
    final double heightLower = minHeight + Random().nextDouble() * (width / 1.8);
    // 生成上眼睑起始和结束点的随机偏移量
    final double p0UpperRandX = Random().nextDouble() * 0.4 - 0.2; // 上眼睑起始点X偏移 [-0.2, 0.2]
    final double p3UpperRandX = Random().nextDouble() * 0.4 - 0.2; // 上眼睑结束点X偏移 [-0.2, 0.2]
    final double p0UpperRandY = Random().nextDouble() * 0.4 - 0.2; // 上眼睑起始点Y偏移 [-0.2, 0.2]
    final double p3UpperRandY = Random().nextDouble() * 0.4 - 0.2; // 上眼睑结束点Y偏移 [-0.2, 0.2]
    final double offsetUpperLeftRandY = Random().nextDouble(); // 上眼睑左侧控制点Y偏移 [0, 1]
    final double offsetUpperRightRandY = Random().nextDouble(); // 上眼睑右侧控制点Y偏移 [0, 1]

    // 计算上眼睑的起始和结束点
    final Point2D p0Upper = Point2D(-width / 2 + p0UpperRandX * width / 16, p0UpperRandY * heightUpper / 16);
    final Point2D p3Upper = Point2D(width / 2 + p3UpperRandX * width / 16, p3UpperRandY * heightUpper / 16);
    final double eyeTrueWidth = p3Upper.x - p0Upper.x; // 眼睛的实际宽度

    // 计算上眼睑控制点偏移量，用于创建不对称效果
    final double offsetUpperLeftX = RandomUtils.randomFromInterval(-eyeTrueWidth / 12.0, eyeTrueWidth / 4.0); // 上眼睑左侧控制点X偏移
    final double offsetUpperRightX = RandomUtils.randomFromInterval(-eyeTrueWidth / 12.0, eyeTrueWidth / 4.0); // 上眼睑右侧控制点X偏移
    final double offsetUpperLeftY = offsetUpperLeftRandY * heightUpper / 3; // 上眼睑左侧控制点Y偏移
    final double offsetUpperRightY = offsetUpperRightRandY * heightUpper / 3; // 上眼睑右侧控制点Y偏移

    // 计算下眼睑控制点偏移量
    final double offsetLowerLeftX = RandomUtils.randomFromInterval(offsetUpperLeftX, eyeTrueWidth / 4.0); // 下眼睑左侧控制点X偏移
    final double offsetLowerRightX = RandomUtils.randomFromInterval(offsetUpperRightX, eyeTrueWidth / 4.0); // 下眼睑右侧控制点X偏移
    final double offsetLowerLeftY = RandomUtils.randomFromInterval(-offsetUpperLeftY + 1, heightLower / 3); // 下眼睑左侧控制点Y偏移
    final double offsetLowerRightY = RandomUtils.randomFromInterval(-offsetUpperRightY + 1, heightLower / 3); // 下眼睑右侧控制点Y偏移

    // 生成贝塞尔曲线的收敛参数，用于控制眼睑形状的混合
    final double leftConverge0 = Random().nextDouble(); // 左侧收敛参数0 [0, 1]
    final double rightConverge0 = Random().nextDouble(); // 右侧收敛参数0 [0, 1]
    final double leftConverge1 = Random().nextDouble(); // 左侧收敛参数1 [0, 1]
    final double rightConverge1 = Random().nextDouble(); // 右侧收敛参数1 [0, 1]

    return EyeParameters(
      heightUpper: heightUpper,
      heightLower: heightLower,
      p0UpperRandX: p0UpperRandX,
      p3UpperRandX: p3UpperRandX,
      p0UpperRandY: p0UpperRandY,
      p3UpperRandY: p3UpperRandY,
      offsetUpperLeftRandY: offsetUpperLeftRandY,
      offsetUpperRightRandY: offsetUpperRightRandY,
      eyeTrueWidth: eyeTrueWidth,
      offsetUpperLeftX: offsetUpperLeftX,
      offsetUpperRightX: offsetUpperRightX,
      offsetUpperLeftY: offsetUpperLeftY,
      offsetUpperRightY: offsetUpperRightY,
      offsetLowerLeftX: offsetLowerLeftX,
      offsetLowerRightX: offsetLowerRightX,
      offsetLowerLeftY: offsetLowerLeftY,
      offsetLowerRightY: offsetLowerRightY,
      leftConverge0: leftConverge0,
      rightConverge0: rightConverge0,
      leftConverge1: leftConverge1,
      rightConverge1: rightConverge1,
    );
  }

  /// 根据参数生成眼睛的点集合
  /// [rands] 眼睛参数
  /// [width] 眼睛宽度，默认为50
  static EyePoints generateEyePoints(EyeParameters rands, {double width = 50}) {
    // 计算上眼睑的起始和结束点
    final Point2D p0Upper = Point2D(-width / 2 + rands.p0UpperRandX * width / 16, rands.p0UpperRandY * rands.heightUpper / 16);
    final Point2D p3Upper = Point2D(width / 2 + rands.p3UpperRandX * width / 16, rands.p3UpperRandY * rands.heightUpper / 16);
    final Point2D p0Lower = p0Upper; // 下眼睑起始点与上眼睑相同
    final Point2D p3Lower = p3Upper; // 下眼睑结束点与上眼睑相同

    // 计算上眼睑的贝塞尔曲线控制点
    final Point2D p1Upper = Point2D(p0Upper.x + rands.offsetUpperLeftX, p0Upper.y + rands.offsetUpperLeftY); // 上眼睑第一个控制点
    final Point2D p2Upper = Point2D(p3Upper.x - rands.offsetUpperRightX, p3Upper.y + rands.offsetUpperRightY); // 上眼睑第二个控制点

    // 计算下眼睑的贝塞尔曲线控制点
    final Point2D p1Lower = Point2D(p0Lower.x + rands.offsetLowerLeftX, p0Lower.y - rands.offsetLowerLeftY); // 下眼睑第一个控制点
    final Point2D p2Lower = Point2D(p3Lower.x - rands.offsetLowerRightX, p3Lower.y - rands.offsetLowerRightY); // 下眼睑第二个控制点

    // 生成上眼睑的点集合
    List<Point2D> upperEyelidPoints = []; // 主要上眼睑点
    List<Point2D> upperEyelidPointsLeftControl = []; // 左侧控制点影响的上眼睑点
    List<Point2D> upperEyelidPointsRightControl = []; // 右侧控制点影响的上眼睑点

    // 计算上眼睑的混合控制点，用于创建更自然的眼睑形状
    final Point2D upperEyelidLeftControlPoint = Point2D(
      p0Upper.x * (1 - rands.leftConverge0) + p1Lower.x * rands.leftConverge0,
      p0Upper.y * (1 - rands.leftConverge0) + p1Lower.y * rands.leftConverge0
    );
    final Point2D upperEyelidRightControlPoint = Point2D(
      p3Upper.x * (1 - rands.rightConverge0) + p2Lower.x * rands.rightConverge0,
      p3Upper.y * (1 - rands.rightConverge0) + p2Lower.y * rands.rightConverge0
    );

    // 生成100个点来构成上眼睑的贝塞尔曲线
    for (int t = 0; t < 100; t++) {
      final double tValue = t / 100.0; // 参数t从0到1
      upperEyelidPoints.add(BezierUtils.cubicBezier(p0Upper, p1Upper, p2Upper, p3Upper, tValue));
      upperEyelidPointsLeftControl.add(BezierUtils.cubicBezier(upperEyelidLeftControlPoint, p0Upper, p1Upper, p2Upper, tValue));
      upperEyelidPointsRightControl.add(BezierUtils.cubicBezier(p1Upper, p2Upper, p3Upper, upperEyelidRightControlPoint, tValue));
    }

    // 混合不同的控制点影响，创建更自然的眼睑形状
    // 使用权重函数来平滑过渡不同区域的影响
    for (int i = 0; i < 75; i++) {
      final double weight = pow((75.0 - i) / 75.0, 2).toDouble(); // 二次权重函数，创建平滑过渡
      // 混合左侧控制点的影响
      upperEyelidPoints[i] = Point2D(
        upperEyelidPoints[i].x * (1 - weight) + upperEyelidPointsLeftControl[i + 25].x * weight,
        upperEyelidPoints[i].y * (1 - weight) + upperEyelidPointsLeftControl[i + 25].y * weight
      );
      // 混合右侧控制点的影响
      upperEyelidPoints[i + 25] = Point2D(
        upperEyelidPoints[i + 25].x * weight + upperEyelidPointsRightControl[i].x * (1 - weight),
        upperEyelidPoints[i + 25].y * weight + upperEyelidPointsRightControl[i].y * (1 - weight)
      );
    }

    // 生成下眼睑的点集合
    List<Point2D> lowerEyelidPoints = []; // 主要下眼睑点
    List<Point2D> lowerEyelidPointsLeftControl = []; // 左侧控制点影响的下眼睑点
    List<Point2D> lowerEyelidPointsRightControl = []; // 右侧控制点影响的下眼睑点

    // 计算下眼睑的混合控制点，用于创建更自然的眼睑形状
    final Point2D lowerEyelidLeftControlPoint = Point2D(
      p0Lower.x * (1 - rands.leftConverge0) + p1Upper.x * rands.leftConverge0,
      p0Lower.y * (1 - rands.leftConverge0) + p1Upper.y * rands.leftConverge0
    );
    final Point2D lowerEyelidRightControlPoint = Point2D(
      p3Lower.x * (1 - rands.rightConverge1) + p2Upper.x * rands.rightConverge1,
      p3Lower.y * (1 - rands.rightConverge1) + p2Upper.y * rands.rightConverge1
    );

    // 生成100个点来构成下眼睑的贝塞尔曲线
    for (int t = 0; t < 100; t++) {
      final double tValue = t / 100.0; // 参数t从0到1
      lowerEyelidPoints.add(BezierUtils.cubicBezier(p0Lower, p1Lower, p2Lower, p3Lower, tValue));
      lowerEyelidPointsLeftControl.add(BezierUtils.cubicBezier(lowerEyelidLeftControlPoint, p0Lower, p1Lower, p2Lower, tValue));
      lowerEyelidPointsRightControl.add(BezierUtils.cubicBezier(p1Lower, p2Lower, p3Lower, lowerEyelidRightControlPoint, tValue));
    }

    // 混合不同的控制点影响，创建更自然的下眼睑形状
    // 使用权重函数来平滑过渡不同区域的影响
    for (int i = 0; i < 75; i++) {
      final double weight = pow((75.0 - i) / 75.0, 2).toDouble(); // 二次权重函数，创建平滑过渡
      // 混合左侧控制点的影响
      lowerEyelidPoints[i] = Point2D(
        lowerEyelidPoints[i].x * (1 - weight) + lowerEyelidPointsLeftControl[i + 25].x * weight,
        lowerEyelidPoints[i].y * (1 - weight) + lowerEyelidPointsLeftControl[i + 25].y * weight
      );
      // 混合右侧控制点的影响
      lowerEyelidPoints[i + 25] = Point2D(
        lowerEyelidPoints[i + 25].x * weight + lowerEyelidPointsRightControl[i].x * (1 - weight),
        lowerEyelidPoints[i + 25].y * weight + lowerEyelidPointsRightControl[i].y * (1 - weight)
      );
    }

    // 翻转Y坐标（在SVG中，底部是y+，顶部是y-）
    // 这里调整坐标系以适应Flutter的绘制坐标系
    for (int i = 0; i < 100; i++) {
      lowerEyelidPoints[i] = Point2D(lowerEyelidPoints[i].x, -lowerEyelidPoints[i].y);
      upperEyelidPoints[i] = Point2D(upperEyelidPoints[i].x, -upperEyelidPoints[i].y);
    }

    // 计算眼睛的中心点（使用中间点的平均值）
    final Point2D eyeCenter = Point2D(
      (upperEyelidPoints[50].x + lowerEyelidPoints[50].x) / 2.0,
      (upperEyelidPoints[50].y + lowerEyelidPoints[50].y) / 2.0
    );

    // 将所有点平移到以眼睛中心为原点的坐标系
    for (int i = 0; i < 100; i++) {
      lowerEyelidPoints[i] = lowerEyelidPoints[i] - eyeCenter;
      upperEyelidPoints[i] = upperEyelidPoints[i] - eyeCenter;
    }

    return EyePoints(
      upper: upperEyelidPoints,
      lower: lowerEyelidPoints,
      center: const Point2D(0, 0),
    );
  }

  /// 生成左右两只眼睛
  /// [width] 眼睛的基础宽度，默认为50
  /// 返回包含左右眼睛点集合的BothEyePoints对象
  static BothEyePoints generateBothEyes({double width = 50}) {
    // 生成左眼的基础参数
    final EyeParameters randsLeft = generateEyeParameters(width);
    // 为右眼创建带有变化的参数，确保左右眼不完全对称
    final EyeParameters randsRight = randsLeft.copyWithVariation();

    // 生成左右眼的点集合
    EyePoints leftEye = generateEyePoints(randsLeft, width: width);
    final EyePoints rightEye = generateEyePoints(randsRight, width: width);

    // 通过镜像X坐标来创建左眼（因为左眼是右眼的镜像）
    final List<Point2D> leftUpper = leftEye.upper.map((point) => Point2D(-point.x, point.y)).toList();
    final List<Point2D> leftLower = leftEye.lower.map((point) => Point2D(-point.x, point.y)).toList();

    // 重新构建左眼对象，使用镜像后的点
    leftEye = EyePoints(
      upper: leftUpper,
      lower: leftLower,
      center: Point2D(-leftEye.center.x, leftEye.center.y), // 中心点也要镜像
    );

    return BothEyePoints(
      left: leftEye,
      right: rightEye,
    );
  }
}
