import 'dart:math';
import '../eye/eye_shape.dart'; // Reuse Point2D, RandomUtils, and BezierUtils
import '../face/face_shape.dart'; // For EggShapeUtils

/// 用于生成嘴巴形状的参数
class MouthParameters {
  final double faceHeight; // 脸部高度
  final double faceWidth; // 脸部宽度
  final List<Point2D> faceContour; // 脸部轮廓点
  
  const MouthParameters({
    required this.faceHeight,
    required this.faceWidth,
    required this.faceContour,
  });
}

/// 包含嘴巴形状的生成点
class MouthPoints {
  final List<Point2D> points; // 嘴巴点
  final Point2D center; // 嘴巴中心点
  final MouthType type; // 嘴巴类型
  
  const MouthPoints({
    required this.points,
    required this.center,
    required this.type,
  });
}

/// 嘴巴形状的类型
enum MouthType {
  smile,      // U型微笑
  smirk,      // 修改后的U型微笑
  oval,       // 椭圆形嘴巴
}



/// 用于生成嘴巴形状的主要类
class MouthShapeGenerator {
  /// 生成U型微笑嘴巴 (类型 0)
  static MouthPoints generateMouthShape0(MouthParameters params) {
    final double faceHeight = params.faceHeight;
    final double faceWidth = params.faceWidth;
    
    // Choose points for mouth corners
    final double mouthRightY = RandomUtils.randomFromInterval(faceHeight / 7, faceHeight / 3.5);
    final double mouthLeftY = RandomUtils.randomFromInterval(faceHeight / 7, faceHeight / 3.5);
    final double mouthRightX = RandomUtils.randomFromInterval(faceWidth / 12, faceWidth / 3.5);
    final double mouthLeftX = -mouthRightX + RandomUtils.randomFromInterval(-faceWidth / 20, faceWidth / 20);
    
    final Point2D mouthRight = Point2D(mouthRightX, mouthRightY);
    final Point2D mouthLeft = Point2D(mouthLeftX, mouthLeftY);
    
    // Control points for the Bezier curve
    final Point2D controlPoint0 = Point2D(
      RandomUtils.randomFromInterval(0, mouthRightX),
      RandomUtils.randomFromInterval(mouthLeftY + 5, faceHeight / 1.5)
    );
    final Point2D controlPoint1 = Point2D(
      RandomUtils.randomFromInterval(mouthLeftX, 0),
      RandomUtils.randomFromInterval(mouthLeftY + 5, faceHeight / 1.5)
    );
    
    final List<Point2D> mouthPoints = [];
    
    // Generate first curve
    for (double i = 0; i < 1; i += 0.01) {
      mouthPoints.add(BezierUtils.cubicBezier(mouthLeft, controlPoint1, controlPoint0, mouthRight, i));
    }
    
    // Randomly choose to close the mouth or create a different bottom
    if (Random().nextDouble() > 0.5) {
      // Close with another curve
      for (double i = 0; i < 1; i += 0.01) {
        mouthPoints.add(BezierUtils.cubicBezier(mouthRight, controlPoint0, controlPoint1, mouthLeft, i));
      }
    } else {
      // Create a modified bottom
      final double yOffsetPortion = RandomUtils.randomFromInterval(0, 0.8);
      for (int i = 0; i < 100; i++) {
        final double t = i / 100.0;
        final double x = mouthPoints[99].x * (1 - t) + mouthPoints[0].x * t;
        final double y = (mouthPoints[99].y * (1 - t) + mouthPoints[0].y * t) * (1 - yOffsetPortion) +
                        mouthPoints[99 - i].y * yOffsetPortion;
        mouthPoints.add(Point2D(x, y));
      }
    }
    
    // Calculate center
    final Point2D center = Point2D(
      (mouthRight.x + mouthLeft.x) / 2,
      mouthPoints.isNotEmpty ? mouthPoints[mouthPoints.length ~/ 2].y : 0
    );
    
    return MouthPoints(
      points: mouthPoints,
      center: center,
      type: MouthType.smile,
    );
  }

  /// 生成具有转换的修改后的U型微笑嘴巴 (类型 1)
  static MouthPoints generateMouthShape1(MouthParameters params) {
      final double faceHeight = params.faceHeight;
      final double faceWidth = params.faceWidth;
  
      // Choose points for mouth corners
      final double mouthRightY = RandomUtils.randomFromInterval(faceHeight / 7, faceHeight / 4);
      final double mouthLeftY = RandomUtils.randomFromInterval(faceHeight / 7, faceHeight / 4);
      final double mouthRightX = RandomUtils.randomFromInterval(faceWidth / 12, faceWidth / 3.5);
      final double mouthLeftX = -mouthRightX + RandomUtils.randomFromInterval(-faceWidth / 20, faceWidth / 20);
  
      final Point2D mouthRight = Point2D(mouthRightX, mouthRightY);
      final Point2D mouthLeft = Point2D(mouthLeftX, mouthLeftY);
  
      // Control points for the Bezier curve
      final Point2D controlPoint0 = Point2D(
        RandomUtils.randomFromInterval(0, mouthRightX),
        RandomUtils.randomFromInterval(mouthLeftY + 5, faceHeight / 1.5)
      );
      final Point2D controlPoint1 = Point2D(
        RandomUtils.randomFromInterval(mouthLeftX, 0),
        RandomUtils.randomFromInterval(mouthLeftY + 5, faceHeight / 1.5)
      );
  
      final List<Point2D> mouthPoints = [];
  
      // Generate first curve
      for (double i = 0; i < 1; i += 0.01) {
        mouthPoints.add(BezierUtils.cubicBezier(mouthLeft, controlPoint1, controlPoint0, mouthRight, i));
      }
  
      // Calculate initial center
      Point2D center = Point2D(
        (mouthRight.x + mouthLeft.x) / 2,
        mouthPoints.length >= 50 ? (mouthPoints[25].y / 2 + mouthPoints[75].y / 2) : mouthPoints[0].y
      );
  
      // Randomly choose to close the mouth or create a different bottom
      if (Random().nextDouble() > 0.5) {
        // Close with another curve
        for (double i = 0; i < 1; i += 0.01) {
          mouthPoints.add(BezierUtils.cubicBezier(mouthRight, controlPoint0, controlPoint1, mouthLeft, i));
        }
      } else {
        // Create a modified bottom
        final double yOffsetPortion = RandomUtils.randomFromInterval(0, 0.8);
        for (int i = 0; i < 100; i++) {
          final double t = i / 100.0;
          final double x = mouthPoints[99].x * (1 - t) + mouthPoints[0].x * t;
          final double y = (mouthPoints[99].y * (1 - t) + mouthPoints[0].y * t) * (1 - yOffsetPortion) +
                          mouthPoints[99 - i].y * yOffsetPortion;
          mouthPoints.add(Point2D(x, y));
        }
      }
  
      // Apply transformations: translate to center, rotate 180°, scale, translate back
      final List<Point2D> transformedPoints = [];
      for (final Point2D point in mouthPoints) {
        // Translate to center
        double x = point.x - center.x;
        double y = point.y - center.y;
  
        // Rotate 180 degrees (flip y)
        y = -y;
  
        // Scale smaller
        x = x * 0.6;
        y = y * 0.6;
  
        // Translate back
        x += center.x;
        y += center.y * 0.8;
  
        transformedPoints.add(Point2D(x, y));
      }
  
      return MouthPoints(
        points: transformedPoints,
        center: Point2D(center.x, center.y * 0.8),
        type: MouthType.smirk,
      );
    }
  
    /// 生成具有旋转的椭圆形嘴巴 (类型 2)
  static MouthPoints generateMouthShape2(MouthParameters params) {
      final double faceHeight = params.faceHeight;
      final double faceWidth = params.faceWidth;
  
      // Generate a random center
      final Point2D center = Point2D(
        RandomUtils.randomFromInterval(-faceWidth / 8, faceWidth / 8),
        RandomUtils.randomFromInterval(faceHeight / 4, faceHeight / 2.5)
      );
  
      // Generate egg shape points
      final double a = RandomUtils.randomFromInterval(faceWidth / 12, faceWidth / 5);
      final double b = RandomUtils.randomFromInterval(faceHeight / 20, faceHeight / 10);
      List<Point2D> mouthPoints = EggShapeUtils.getEggShapePoints(a, b, 0.001, 50);
  
      // Apply random rotation
      final double randomRotationDegree = RandomUtils.randomFromInterval(-pi / 9.5, pi / 9.5);
      final List<Point2D> rotatedPoints = [];
  
      for (final Point2D point in mouthPoints) {
        // Rotate the point
        final double x = point.x * cos(randomRotationDegree) - point.y * sin(randomRotationDegree);
        final double y = point.x * sin(randomRotationDegree) + point.y * cos(randomRotationDegree);
  
        // Translate to center
        rotatedPoints.add(Point2D(x + center.x, y + center.y));
      }
  
      return MouthPoints(
        points: rotatedPoints,
        center: center,
        type: MouthType.oval,
      );
    }
  
    /// 通过选择可用类型来生成随机嘴巴形状
  static MouthPoints generateRandomMouth(MouthParameters params) {
      final int shapeType = Random().nextInt(3);
  
      switch (shapeType) {
        case 0:
          return generateMouthShape0(params);
        case 1:
          return generateMouthShape1(params);
        case 2:
          return generateMouthShape2(params);
        default:
          return generateMouthShape0(params);
      }
    }
  
    /// 生成具有特定类型的嘴巴
    static MouthPoints generateMouthByType(MouthParameters params, MouthType type) {
      switch (type) {
        case MouthType.smile:
          return generateMouthShape0(params);
        case MouthType.smirk:
          return generateMouthShape1(params);
        case MouthType.oval:
          return generateMouthShape2(params);
      }
    }
  }
