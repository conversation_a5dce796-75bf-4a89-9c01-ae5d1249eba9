import 'dart:math';
import '../eye/eye_shape.dart'; // Reuse Point2D, RandomUtils, and BezierUtils
import '../face/face_shape.dart'; // For EggShapeUtils

/// 用于生成嘴巴形状的参数
class MouthParameters {
  final double faceHeight; // 脸部高度
  final double faceWidth; // 脸部宽度
  final List<Point2D> faceContour; // 脸部轮廓点
  
  const MouthParameters({
    required this.faceHeight,
    required this.faceWidth,
    required this.faceContour,
  });
}

/// 包含嘴巴形状的生成点
class MouthPoints {
  final List<Point2D> points; // 嘴巴点
  final Point2D center; // 嘴巴中心点
  final MouthType type; // 嘴巴类型
  
  const MouthPoints({
    required this.points,
    required this.center,
    required this.type,
  });
}

/// 嘴巴形状的类型
enum MouthType {
  smile,      // U型微笑
  smirk,      // 修改后的U型微笑
  oval,       // 椭圆形嘴巴
}



/// 用于生成嘴巴形状的主要类
class MouthShapeGenerator {
  /// Helper function to get the horizontal bounds of the face contour at a specific Y level
  /// This is used to ensure the mouth stays within the face's horizontal limits.
  static List<double> _getFaceHorizontalBoundsAtY(List<Point2D> faceContour, double targetY, double tolerance, double fallbackFaceWidth) {
    double minX = double.infinity;
    double maxX = double.negativeInfinity;
    bool foundPoints = false;

    for (final p in faceContour) {
      if (p.y >= targetY - tolerance && p.y <= targetY + tolerance) {
        if (p.x < minX) minX = p.x;
        if (p.x > maxX) maxX = p.x;
        foundPoints = true;
      }
    }
    if (!foundPoints || minX == double.infinity || maxX == double.negativeInfinity) {
      // Fallback if no points found in the range or bounds are invalid
      // Use the overall face width from params as a fallback.
      return [-fallbackFaceWidth / 2, fallbackFaceWidth / 2];
    }
    return [minX, maxX];
  }

  /// 生成U型微笑嘴巴 (类型 0)
  static MouthPoints generateMouthShape0(MouthParameters params) {
    final double faceHeight = params.faceHeight;
    final double faceWidth = params.faceWidth;
    
    // Choose points for mouth corners
    // Adjusted range for mouthRightY and mouthLeftY to push the mouth down
    final double mouthRightY = RandomUtils.randomFromInterval(faceHeight / 4, faceHeight / 3); // Adjusted to be higher
    final double mouthLeftY = RandomUtils.randomFromInterval(faceHeight / 4, faceHeight / 3); // Adjusted to be higher
    
    // Calculate the average Y for the mouth to find corresponding face bounds
    final double mouthAvgY = (mouthRightY + mouthLeftY) / 2;
    // Use a tolerance to capture points around the mouth's vertical level
    final double yTolerance = faceHeight / 20; // A small percentage of face height
    final List<double> faceBoundsX = _getFaceHorizontalBoundsAtY(params.faceContour, mouthAvgY, yTolerance, faceWidth);
    final double effectiveFaceMinX = faceBoundsX[0];
    final double effectiveFaceMaxX = faceBoundsX[1];
    final double effectiveFaceHalfWidth = (effectiveFaceMaxX - effectiveFaceMinX) / 2;

    // Adjust mouthRightX and mouthLeftX ranges based on effectiveFaceHalfWidth
    // Ensure mouthRightX is positive and within the right half of the face
    // Ensure mouthLeftX is negative and within the left half of the face
    // The mouth width should be a fraction of the effective face width.
    // Let's aim for mouth width to be between 40% and 70% of the effective face width.
    // So, mouthRightX (positive half-width) should be between 20% and 35% of effectiveFaceHalfWidth.
    final double minMouthHalfWidthRatio = 0.2; // 40% of effective face width
    final double maxMouthHalfWidthRatio = 0.35; // 70% of effective face width

    final double adjustedMouthRightX = RandomUtils.randomFromInterval(
      effectiveFaceHalfWidth * minMouthHalfWidthRatio,
      effectiveFaceHalfWidth * maxMouthHalfWidthRatio,
    );
    // mouthLeftX should be symmetric or slightly offset from -adjustedMouthRightX
    final double adjustedMouthLeftX = -adjustedMouthRightX + RandomUtils.randomFromInterval(-faceWidth / 80, faceWidth / 80); // Smaller skew range
    
    final Point2D mouthRight = Point2D(adjustedMouthRightX, mouthRightY);
    final Point2D mouthLeft = Point2D(adjustedMouthLeftX, mouthLeftY);
    
    // Control points for the Bezier curve
    final Point2D controlPoint0 = Point2D(
      RandomUtils.randomFromInterval(0, adjustedMouthRightX),
      // Adjusted upper bound for control point Y to limit upward curve relative to mouth base
      RandomUtils.randomFromInterval(mouthLeftY + 2, mouthLeftY + faceHeight / 25) // Reduced max upward curve
    );
    final Point2D controlPoint1 = Point2D(
      RandomUtils.randomFromInterval(adjustedMouthLeftX, 0),
      // Adjusted upper bound for control point Y to limit upward curve relative to mouth base
      RandomUtils.randomFromInterval(mouthLeftY + 2, mouthLeftY + faceHeight / 25) // Reduced max upward curve
    );
    
    final List<Point2D> mouthPoints = [];
    
    // Generate first curve
    for (double i = 0; i < 1; i += 0.01) {
      mouthPoints.add(BezierUtils.cubicBezier(mouthLeft, controlPoint1, controlPoint0, mouthRight, i));
    }
    
    // Randomly choose to close the mouth or create a different bottom
    if (Random().nextDouble() > 0.5) {
      // Close with another curve
      for (double i = 0; i < 1; i += 0.01) {
        mouthPoints.add(BezierUtils.cubicBezier(mouthRight, controlPoint0, controlPoint1, mouthLeft, i));
      }
    } else {
      // Create a modified bottom
      final double yOffsetPortion = RandomUtils.randomFromInterval(0, 0.8);
      for (int i = 0; i < 100; i++) {
        final double t = i / 100.0;
        final double x = mouthPoints[99].x * (1 - t) + mouthPoints[0].x * t;
        final double y = (mouthPoints[99].y * (1 - t) + mouthPoints[0].y * t) * (1 - yOffsetPortion) +
                        mouthPoints[99 - i].y * yOffsetPortion;
        mouthPoints.add(Point2D(x, y));
      }
    }
    
    // Calculate center
    final Point2D center = Point2D(
      (mouthRight.x + mouthLeft.x) / 2,
      mouthPoints.isNotEmpty ? mouthPoints[mouthPoints.length ~/ 2].y : 0
    );
    
    return MouthPoints(
      points: mouthPoints,
      center: center,
      type: MouthType.smile,
    );
  }

  /// 生成具有转换的修改后的U型微笑嘴巴 (类型 1)
  static MouthPoints generateMouthShape1(MouthParameters params) {
      final double faceHeight = params.faceHeight;
      final double faceWidth = params.faceWidth;
  
      // Choose points for mouth corners
      // Adjusted range for mouthRightY and mouthLeftY to push the mouth down
      final double mouthRightY = RandomUtils.randomFromInterval(faceHeight / 4, faceHeight / 3); // Adjusted to be higher
      final double mouthLeftY = RandomUtils.randomFromInterval(faceHeight / 4, faceHeight / 3); // Adjusted to be higher
  
      // Calculate the average Y for the mouth to find corresponding face bounds
      final double mouthAvgY = (mouthRightY + mouthLeftY) / 2;
      final double yTolerance = faceHeight / 20;
      final List<double> faceBoundsX = _getFaceHorizontalBoundsAtY(params.faceContour, mouthAvgY, yTolerance, faceWidth);
      final double effectiveFaceMinX = faceBoundsX[0];
      final double effectiveFaceMaxX = faceBoundsX[1];
      final double effectiveFaceHalfWidth = (effectiveFaceMaxX - effectiveFaceMinX) / 2;

      final double minMouthHalfWidthRatio = 0.2;
      final double maxMouthHalfWidthRatio = 0.35;

      final double adjustedMouthRightX = RandomUtils.randomFromInterval(
        effectiveFaceHalfWidth * minMouthHalfWidthRatio,
        effectiveFaceHalfWidth * maxMouthHalfWidthRatio,
      );
      final double adjustedMouthLeftX = -adjustedMouthRightX + RandomUtils.randomFromInterval(-faceWidth / 80, faceWidth / 80);
  
      final Point2D mouthRight = Point2D(adjustedMouthRightX, mouthRightY);
      final Point2D mouthLeft = Point2D(adjustedMouthLeftX, mouthLeftY);
  
      // Control points for the Bezier curve
      final Point2D controlPoint0 = Point2D(
        RandomUtils.randomFromInterval(0, adjustedMouthRightX),
        // Adjusted upper bound for control point Y to limit upward curve relative to mouth base
        RandomUtils.randomFromInterval(mouthLeftY + 2, mouthLeftY + faceHeight / 25) // Reduced max upward curve
      );
      final Point2D controlPoint1 = Point2D(
        RandomUtils.randomFromInterval(adjustedMouthLeftX, 0),
        // Adjusted upper bound for control point Y to limit upward curve relative to mouth base
        RandomUtils.randomFromInterval(mouthLeftY + 2, mouthLeftY + faceHeight / 25) // Reduced max upward curve
      );
  
      final List<Point2D> mouthPoints = [];
  
      // Generate first curve
      for (double i = 0; i < 1; i += 0.01) {
        mouthPoints.add(BezierUtils.cubicBezier(mouthLeft, controlPoint1, controlPoint0, mouthRight, i));
      }
  
      // Calculate initial center
      Point2D center = Point2D(
        (mouthRight.x + mouthLeft.x) / 2,
        mouthPoints.length >= 50 ? (mouthPoints[25].y / 2 + mouthPoints[75].y / 2) : mouthPoints[0].y
      );
  
      // Randomly choose to close the mouth or create a different bottom
      if (Random().nextDouble() > 0.5) {
        // Close with another curve
        for (double i = 0; i < 1; i += 0.01) {
          mouthPoints.add(BezierUtils.cubicBezier(mouthRight, controlPoint0, controlPoint1, mouthLeft, i));
        }
      } else {
        // Create a modified bottom
        final double yOffsetPortion = RandomUtils.randomFromInterval(0, 0.8);
        for (int i = 0; i < 100; i++) {
          final double t = i / 100.0;
          final double x = mouthPoints[99].x * (1 - t) + mouthPoints[0].x * t;
          final double y = (mouthPoints[99].y * (1 - t) + mouthPoints[0].y * t) * (1 - yOffsetPortion) +
                          mouthPoints[99 - i].y * yOffsetPortion;
          mouthPoints.add(Point2D(x, y));
        }
      }
  
      // Apply transformations: translate to center, rotate 180°, scale, translate back
      final List<Point2D> transformedPoints = [];
      for (final Point2D point in mouthPoints) {
        // Translate to center
        double x = point.x - center.x;
        double y = point.y - center.y;
  
        // Rotate 180 degrees (flip y)
        y = -y;
  
        // Scale smaller
        x = x * 0.6;
        y = y * 0.6;
  
        // Translate back
        x += center.x;
        y += center.y * 0.8;
  
        transformedPoints.add(Point2D(x, y));
      }
  
      return MouthPoints(
        points: transformedPoints,
        center: Point2D(center.x, center.y * 0.8),
        type: MouthType.smirk,
      );
    }
  
    /// 生成具有旋转的椭圆形嘴巴 (类型 2)
  static MouthPoints generateMouthShape2(MouthParameters params) {
      final double faceHeight = params.faceHeight;
      final double faceWidth = params.faceWidth;
  
      // Generate a random center - reduce X offset to keep closer to center
      // Narrowed range for center.x to keep oval mouth more centered
      final Point2D center = Point2D(
        RandomUtils.randomFromInterval(-faceWidth / 25, faceWidth / 25),
        // Adjusted range for center.y to push the oval mouth down
        RandomUtils.randomFromInterval(faceHeight / 4, faceHeight / 3) // Adjusted to be higher
      );

      // Calculate the average Y for the oval mouth to find corresponding face bounds
      final double ovalAvgY = center.y;
      final double yTolerance = faceHeight / 20;
      final List<double> faceBoundsX = _getFaceHorizontalBoundsAtY(params.faceContour, ovalAvgY, yTolerance, faceWidth);
      final double effectiveFaceMinX = faceBoundsX[0];
      final double effectiveFaceMaxX = faceBoundsX[1];
      final double effectiveFaceHalfWidth = (effectiveFaceMaxX - effectiveFaceMinX) / 2;
  
      // Generate egg shape points
      // Adjust 'a' (horizontal radius) based on effectiveFaceHalfWidth
      // Let's aim for 'a' to be between 15% and 30% of the effective face half-width.
      final double minOvalRadiusRatio = 0.15;
      final double maxOvalRadiusRatio = 0.3;
      final double a = RandomUtils.randomFromInterval(
        effectiveFaceHalfWidth * minOvalRadiusRatio,
        effectiveFaceHalfWidth * maxOvalRadiusRatio,
      );
      final double b = RandomUtils.randomFromInterval(faceHeight / 20, faceHeight / 10);
      List<Point2D> mouthPoints = EggShapeUtils.getEggShapePoints(a, b, 0.001, 50);
  
      // Apply random rotation
      // Reduced random rotation degree to make it less skewed
      final double randomRotationDegree = RandomUtils.randomFromInterval(-pi / 20, pi / 20); // Reduced from pi/15
      final List<Point2D> rotatedPoints = [];
  
      for (final Point2D point in mouthPoints) {
        // Rotate the point
        final double x = point.x * cos(randomRotationDegree) - point.y * sin(randomRotationDegree);
        final double y = point.x * sin(randomRotationDegree) + point.y * cos(randomRotationDegree);
  
        // Translate to center
        rotatedPoints.add(Point2D(x + center.x, y + center.y));
      }
  
      return MouthPoints(
        points: rotatedPoints,
        center: center,
        type: MouthType.oval,
      );
    }
  
    /// 通过选择可用类型来生成随机嘴巴形状
  static MouthPoints generateRandomMouth(MouthParameters params) {
      final int shapeType = Random().nextInt(3);
  
      switch (shapeType) {
        case 0:
          return generateMouthShape0(params);
        case 1:
          return generateMouthShape1(params);
        case 2:
          return generateMouthShape2(params);
        default:
          return generateMouthShape0(params);
      }
    }
  
    /// 生成具有特定类型的嘴巴
    static MouthPoints generateMouthByType(MouthParameters params, MouthType type) {
      switch (type) {
        case MouthType.smile:
          return generateMouthShape0(params);
        case MouthType.smirk:
          return generateMouthShape1(params);
        case MouthType.oval:
          return generateMouthShape2(params);
      }
    }
  }
