import 'package:flutter/material.dart';
import 'mouth_shape.dart';
import '../eye/eye_shape.dart'; // For Point2D

/// Custom painter for drawing mouth shapes
class MouthPainter extends CustomPainter {
  final MouthPoints mouthPoints;
  final Color mouthColor;
  final double strokeWidth;
  final bool filled;

  MouthPainter({
    required this.mouthPoints,
    this.mouthColor = Colors.red,
    this.strokeWidth = 2.0,
    this.filled = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = mouthColor
      ..strokeWidth = strokeWidth
      ..style = filled ? PaintingStyle.fill : PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    // Calculate center and scale
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double scale = size.width / 200; // Adjust scale based on widget size

    // Create path for mouth
    final Path mouthPath = Path();
    if (mouthPoints.points.isNotEmpty) {
      final Point2D firstPoint = mouthPoints.points.first;
      mouthPath.moveTo(
        centerX + firstPoint.x * scale,
        centerY + firstPoint.y * scale,
      );

      for (int i = 1; i < mouthPoints.points.length; i++) {
        final Point2D point = mouthPoints.points[i];
        mouthPath.lineTo(
          centerX + point.x * scale,
          centerY + point.y * scale,
        );
      }

      // Close the path for filled mouths
      if (filled) {
        mouthPath.close();
      }
    }

    // Draw the mouth shape
    canvas.drawPath(mouthPath, paint);

    // Optionally draw center point for debugging
    if (false) { // Set to true for debugging
      final Paint centerPaint = Paint()
        ..color = Colors.blue
        ..style = PaintingStyle.fill;
      canvas.drawCircle(
        Offset(centerX + mouthPoints.center.x * scale, centerY + mouthPoints.center.y * scale),
        3,
        centerPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}

/// Widget for displaying a single mouth
class MouthWidget extends StatelessWidget {
  final MouthPoints mouthPoints;
  final double size;
  final Color mouthColor;
  final double strokeWidth;
  final bool filled;

  const MouthWidget({
    super.key,
    required this.mouthPoints,
    this.size = 150,
    this.mouthColor = Colors.red,
    this.strokeWidth = 2.0,
    this.filled = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: MouthPainter(
          mouthPoints: mouthPoints,
          mouthColor: mouthColor,
          strokeWidth: strokeWidth,
          filled: filled,
        ),
      ),
    );
  }
}

/// Widget that generates and displays random mouth shapes
class RandomMouthWidget extends StatefulWidget {
  final double size;
  final Color mouthColor;
  final double strokeWidth;
  final bool filled;
  final double faceWidth;
  final double faceHeight;
  final MouthType? fixedType;

  const RandomMouthWidget({
    super.key,
    this.size = 150,
    this.mouthColor = Colors.red,
    this.strokeWidth = 2.0,
    this.filled = false,
    this.faceWidth = 100,
    this.faceHeight = 120,
    this.fixedType,
  });

  @override
  State<RandomMouthWidget> createState() => _RandomMouthWidgetState();
}

class _RandomMouthWidgetState extends State<RandomMouthWidget> {
  late MouthPoints _mouthPoints;

  @override
  void initState() {
    super.initState();
    _generateNewMouth();
  }

  void _generateNewMouth() {
    setState(() {
      final MouthParameters params = MouthParameters(
        faceHeight: widget.faceHeight,
        faceWidth: widget.faceWidth,
        faceContour: [], // Empty for now, can be populated if needed
      );

      if (widget.fixedType != null) {
        _mouthPoints = MouthShapeGenerator.generateMouthByType(params, widget.fixedType!);
      } else {
        _mouthPoints = MouthShapeGenerator.generateRandomMouth(params);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        MouthWidget(
          mouthPoints: _mouthPoints,
          size: widget.size,
          mouthColor: widget.mouthColor,
          strokeWidth: widget.strokeWidth,
          filled: widget.filled,
        ),
        const SizedBox(height: 10),
        Text(
          _getMouthTypeName(_mouthPoints.type),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 10),
        ElevatedButton(
          onPressed: _generateNewMouth,
          child: const Text('Generate New'),
        ),
      ],
    );
  }

  String _getMouthTypeName(MouthType type) {
    switch (type) {
      case MouthType.smile:
        return 'Smile';
      case MouthType.smirk:
        return 'Smirk';
      case MouthType.oval:
        return 'Oval';
    }
  }
}

/// Widget for displaying all mouth types side by side
class AllMouthTypesWidget extends StatefulWidget {
  final double size;
  final double spacing;
  final Color mouthColor;
  final double strokeWidth;
  final bool filled;
  final double faceWidth;
  final double faceHeight;

  const AllMouthTypesWidget({
    super.key,
    this.size = 120,
    this.spacing = 20,
    this.mouthColor = Colors.red,
    this.strokeWidth = 2.0,
    this.filled = false,
    this.faceWidth = 100,
    this.faceHeight = 120,
  });

  @override
  State<AllMouthTypesWidget> createState() => _AllMouthTypesWidgetState();
}

class _AllMouthTypesWidgetState extends State<AllMouthTypesWidget> {
  late List<MouthPoints> _allMouthPoints;

  @override
  void initState() {
    super.initState();
    _generateAllMouths();
  }

  void _generateAllMouths() {
    setState(() {
      final MouthParameters params = MouthParameters(
        faceHeight: widget.faceHeight,
        faceWidth: widget.faceWidth,
        faceContour: [],
      );

      _allMouthPoints = [
        MouthShapeGenerator.generateMouthByType(params, MouthType.smile),
        MouthShapeGenerator.generateMouthByType(params, MouthType.smirk),
        MouthShapeGenerator.generateMouthByType(params, MouthType.oval),
      ];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: _allMouthPoints.asMap().entries.map((entry) {
            final int index = entry.key;
            final MouthPoints mouthPoints = entry.value;
            
            return Row(
              children: [
                if (index > 0) SizedBox(width: widget.spacing),
                Column(
                  children: [
                    MouthWidget(
                      mouthPoints: mouthPoints,
                      size: widget.size,
                      mouthColor: widget.mouthColor,
                      strokeWidth: widget.strokeWidth,
                      filled: widget.filled,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getMouthTypeName(mouthPoints.type),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            );
          }).toList(),
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: _generateAllMouths,
          child: const Text('Generate All New'),
        ),
      ],
    );
  }

  String _getMouthTypeName(MouthType type) {
    switch (type) {
      case MouthType.smile:
        return 'Smile';
      case MouthType.smirk:
        return 'Smirk';
      case MouthType.oval:
        return 'Oval';
    }
  }
}
