import 'dart:math';
import '../eye/eye_shape.dart'; // 导入 Point2D 和 RandomUtils

/// 蛋形点生成工具类
/// Utility class for generating egg-shaped points
class EggShapeUtils {
  /// 使用公式生成蛋形点：x²/a² * (1 + ky) + y²/b² = 1
  /// Generate points for an egg shape using the formula: x²/a² * (1 + ky) + y²/b² = 1
  static List<Point2D> getEggShapePoints(double a, double b, double k, int segmentPoints) {
    final List<Point2D> result = [];
    
    // 第一象限: x正, y正
    for (int i = 0; i < segmentPoints; i++) {
      // 计算角度（带随机偏移）
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      // 计算y坐标
      final double y = sin(degree) * b;
      // 根据蛋形公式计算x坐标（带随机偏移）
      final double x = sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) +
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    // 第二象限: x负, y正
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      final double y = sin(degree) * b;
      // x坐标为负值
      final double x = -sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) +
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    // 第三象限: x负, y负
    for (int i = 0; i < segmentPoints; i++) {
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      // y坐标为负值
      final double y = -sin(degree) * b;
      final double x = -sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) +
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    // 第四象限: x正, y负
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 1.1 / segmentPoints, pi / 1.1 / segmentPoints);
      final double y = -sin(degree) * b;
      final double x = sqrt(((1 - (y * y) / (b * b)) / (1 + k * y)) * a * a) +
          RandomUtils.randomFromInterval(-a / 200.0, a / 200.0);
      result.add(Point2D(x, y));
    }
    
    return result;
  }
}

/// 几何计算工具类
/// Utility class for geometric calculations
class GeometryUtils {
  /// 查找直线与矩形的交点（仅第一象限）
  /// Find intersection points of a line with a rectangle (first quadrant only)
  static Point2D findIntersectionPoints(double radian, double a, double b) {
    // 将角度限制在0-90度范围内
    double clampedRadian = radian.clamp(0.0, pi / 2);
    
    // 检查是否接近90度（垂直）
    if ((clampedRadian - pi / 2).abs() < 0.0001) {
      return Point2D(0, b); // 返回顶部中点
    }
    
    // 计算直线斜率
    final double m = tan(clampedRadian);
    
    // 仅计算第一象限
    final double y = m * a; // 假设与右侧边相交时的y坐标
    if (y < b) {
      // 与右侧边相交
      return Point2D(a, y);
    } else {
      // 与顶部边相交
      final double x = b / m; // 计算与顶部边相交的x坐标
      return Point2D(x, b);
    }
  }
}

/// 矩形脸轮廓点生成工具类
/// Generate rectangular face contour points
class RectangularFaceUtils {
  /// 生成矩形脸轮廓点
  /// Generate rectangular face contour points
  static List<Point2D> generateRectangularFaceContourPoints(double a, double b, int segmentPoints) {
    final List<Point2D> result = [];
    
    // 第一象限: x正, y正
    for (int i = 0; i < segmentPoints; i++) {
      // 计算角度（带较小随机偏移）
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      // 获取与矩形的交点
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      result.add(Point2D(intersection.x, intersection.y));
    }
    
    // 第二象限: x负, y正
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      // x坐标为负值
      result.add(Point2D(-intersection.x, intersection.y));
    }
    
    // 第三象限: x负, y负
    for (int i = 0; i < segmentPoints; i++) {
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      // x和y坐标均为负值
      result.add(Point2D(-intersection.x, -intersection.y));
    }
    
    // 第四象限: x正, y负
    for (int i = segmentPoints; i > 0; i--) {
      final double degree = (pi / 2 / segmentPoints) * i +
          RandomUtils.randomFromInterval(-pi / 11 / segmentPoints, pi / 11 / segmentPoints);
      final Point2D intersection = GeometryUtils.findIntersectionPoints(degree, a, b);
      // y坐标为负值
      result.add(Point2D(intersection.x, -intersection.y));
    }
    
    return result;
  }
}

/// 表示脸部轮廓的生成结果
/// Contains the generated points and properties for a face contour
class FaceContour {
  final List<Point2D> points; // 轮廓点集合
  final double width;         // 脸部宽度
  final double height;        // 脸部高度
  final Point2D center;       // 脸部中心点
  final bool isCircular;      // 是否为圆形脸
  
  const FaceContour({
    required this.points,
    required this.width,
    required this.height,
    required this.center,
    this.isCircular = false,
  });
}

/// 脸部形状生成器（主类）
/// Main class for generating face shapes
class FaceShapeGenerator {
  /// 生成带随机变化的脸部轮廓点
  /// Generate face contour points with random variations
  static FaceContour generateFaceContourPoints({int numPoints = 100}) {
    // 为两个基础脸型生成随机参数 - 适度增加尺寸范围
    final double faceSizeX0 = RandomUtils.randomFromInterval(45, 85); // 第一个脸型的x轴尺寸
    final double faceSizeY0 = RandomUtils.randomFromInterval(60, 90); // 第一个脸型的y轴尺寸
    final double faceSizeY1 = RandomUtils.randomFromInterval(45, 75);  // 第二个脸型的y轴尺寸
    final double faceSizeX1 = RandomUtils.randomFromInterval(60, 90); // 第二个脸型的x轴尺寸
    
    // 生成蛋形参数（k值）带随机正负号
    final double faceK0 = RandomUtils.randomFromInterval(0.001, 0.005) * (Random().nextDouble() > 0.5 ? 1 : -1);
    final double faceK1 = RandomUtils.randomFromInterval(0.001, 0.005) * (Random().nextDouble() > 0.5 ? 1 : -1);
    
    // 生成平移参数
    final double face0TranslateX = RandomUtils.randomFromInterval(-5, 5); // 第一个脸型的x平移
    final double face0TranslateY = RandomUtils.randomFromInterval(-15, 15); // 第一个脸型的y平移
    final double face1TranslateY = RandomUtils.randomFromInterval(-5, 5); // 第二个脸型的y平移
    final double face1TranslateX = RandomUtils.randomFromInterval(-5, 25); // 第二个脸型的x平移
    
    // 随机选择脸型（蛋形或矩形）
    final bool eggOrRect0 = Random().nextDouble() > 0.1; // 90%概率选择蛋形
    final bool eggOrRect1 = Random().nextDouble() > 0.3; // 70%概率选择蛋形
    
    // 生成两个基础脸型点集
    List<Point2D> results0 = eggOrRect0
        ? EggShapeUtils.getEggShapePoints(faceSizeX0, faceSizeY0, faceK0, numPoints)
        : RectangularFaceUtils.generateRectangularFaceContourPoints(faceSizeX0, faceSizeY0, numPoints);
        
    List<Point2D> results1 = eggOrRect1
        ? EggShapeUtils.getEggShapePoints(faceSizeX1, faceSizeY1, faceK1, numPoints)
        : RectangularFaceUtils.generateRectangularFaceContourPoints(faceSizeX1, faceSizeY1, numPoints);
    
    // 应用平移变换
    for (int i = 0; i < results0.length; i++) {
      results0[i] = Point2D(results0[i].x + face0TranslateX, results0[i].y + face0TranslateY);
      results1[i] = Point2D(results1[i].x + face1TranslateX, results1[i].y + face1TranslateY);
    }
    
    // 使用加权混合合并两个形状
    final List<Point2D> results = [];
    Point2D center = const Point2D(0, 0); // 初始化中心点
    
    for (int i = 0; i < results0.length; i++) {
      // 使用偏移索引实现形状交错混合
      final int offsetIndex = (i + (results0.length ~/ 4)) % results0.length;
      // 混合点计算（70%第一个形状 + 30%第二个形状的旋转）
      final Point2D blendedPoint = Point2D(
        results0[i].x * 0.7 + results1[offsetIndex].y * 0.3,
        results0[i].y * 0.7 - results1[offsetIndex].x * 0.3,
      );
      results.add(blendedPoint);
      // 累加点坐标以计算中心
      center = Point2D(center.x + blendedPoint.x, center.y + blendedPoint.y);
    }
    
    // 计算平均中心点
    center = Point2D(center.x / results.length, center.y / results.length);
    
    // 将点集中心移至原点
    final List<Point2D> centeredResults = [];
    for (final Point2D point in results) {
      centeredResults.add(Point2D(point.x - center.x, point.y - center.y));
    }
    
    // 计算脸部宽度和高度
    final double width = centeredResults[0].x - centeredResults[centeredResults.length ~/ 2].x;
    final double height = centeredResults[centeredResults.length ~/ 4].y -
                         centeredResults[(centeredResults.length * 3) ~/ 4].y;
    
    // 添加首点至末尾以闭合形状（添加两个点使曲线更平滑）
    centeredResults.add(centeredResults[0]);
    centeredResults.add(centeredResults[1]);
    
    // 判断是否为圆形脸 (宽高比接近1:1)
    final bool isCircular = (width / height).abs() > 0.9 &&
                          (width / height).abs() < 1.1;
    
    return FaceContour(
      points: centeredResults,
      width: width.abs(),   // 取绝对值确保正值
      height: height.abs(), // 取绝对值确保正值
      center: const Point2D(0, 0), // 中心已移至原点
      isCircular: isCircular,
    );
  }
}
