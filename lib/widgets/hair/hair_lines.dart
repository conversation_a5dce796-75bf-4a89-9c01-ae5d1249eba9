import 'dart:math';
import '../eye/eye_shape.dart'; // For Point2D and RandomUtils

/// 用于贝塞尔曲线计算的实用工具类
class BezierCurveUtils {
  /// 计算阶乘
  static int factorial(int n) {
    if (n <= 1) return 1;
    return n * factorial(n - 1);
  }
  
  /// 计算二项式系数
  static int binomialCoefficient(int n, int k) {
    return factorial(n) ~/ (factorial(k) * factorial(n - k));
  }
  
  /// 计算贝塞尔曲线上的一个点
  static Point2D calculateBezierPoint(double t, List<Point2D> controlPoints) {
    double x = 0, y = 0;
    final int n = controlPoints.length - 1;
    
    for (int i = 0; i <= n; i++) {
      final int binCoeff = binomialCoefficient(n, i);
      final double a = pow(1 - t, n - i).toDouble();
      final double b = pow(t, i).toDouble();
      x += binCoeff * a * b * controlPoints[i].x;
      y += binCoeff * a * b * controlPoints[i].y;
    }
    
    return Point2D(x, y);
  }
  
  /// 计算完整的贝塞尔曲线
  static List<Point2D> computeBezierCurve(List<Point2D> controlPoints, int numberOfPoints) {
    final List<Point2D> curve = [];
    for (int i = 0; i <= numberOfPoints; i++) {
      final double t = i / numberOfPoints;
      final Point2D point = calculateBezierPoint(t, controlPoints);
      curve.add(point);
    }
    return curve;
  }
}

/// 用于基于脸部轮廓生成发线的实用工具类
class HairLinesGenerator {
  /// 生成发线方法 0 - 混合贝塞尔曲线
  static List<List<Point2D>> generateHairLines0(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];

    // Remove the last 2 points (as in the original JS)
    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];

    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 12 + (RandomUtils.randomFromInterval(-2, 2)).round(); // Further reduced hair points

      // Generate first hair line - expand coverage area significantly
      final List<Point2D> hairLine1 = [];
      final int indexOffset1 = (RandomUtils.randomFromInterval(30, 140)).round(); // Further expanded coverage area

      for (int j = 0; j < numHairPoints; j++) {
        final int index = (faceContourCopy.length - (j + indexOffset1)) % faceContourCopy.length;
        // Increase scaling range for larger hair coverage
        final Point2D originalPoint = faceContourCopy[index];
        final double scaleFactor = 0.9 + (j / numHairPoints) * 0.5; // Expanded scaling from 0.9 to 1.4
        // Add slight downward bias to make hair fall more naturally
        final double yBias = j > numHairPoints * 0.4 ? (j / numHairPoints) * 0.2 : 0;
        hairLine1.add(Point2D(
          originalPoint.x * scaleFactor,
          originalPoint.y * scaleFactor + yBias * 12, // Increased downward bias for more natural fall
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      final List<Point2D> d0 = BezierCurveUtils.computeBezierCurve(hairLine1, curvePoints);

      // Generate second hair line - expand coverage area significantly
      final List<Point2D> hairLine2 = [];
      final int indexOffset2 = (RandomUtils.randomFromInterval(30, 140)).round(); // Further expanded coverage area

      for (int j = 0; j < numHairPoints; j++) {
        final int index = (faceContourCopy.length - (-j + indexOffset2)) % faceContourCopy.length;
        // Increase scaling range for larger hair coverage
        final Point2D originalPoint = faceContourCopy[index];
        final double scaleFactor = 0.9 + (j / numHairPoints) * 0.5; // Expanded scaling from 0.9 to 1.4
        // Add slight downward bias to make hair fall more naturally
        final double yBias = j > numHairPoints * 0.4 ? (j / numHairPoints) * 0.2 : 0;
        hairLine2.add(Point2D(
          originalPoint.x * scaleFactor,
          originalPoint.y * scaleFactor + yBias * 12, // Increased downward bias for more natural fall
        ));
      }

      final List<Point2D> d1 = BezierCurveUtils.computeBezierCurve(hairLine2, curvePoints);

      // Blend the two curves
      final List<Point2D> blendedCurve = [];
      for (int j = 0; j < curvePoints; j++) {
        final double t = j / curvePoints;
        final double weight = pow(t, 2).toDouble();
        final double x = d0[j].x * weight + d1[j].x * (1 - weight);
        final double y = d0[j].y * weight + d1[j].y * (1 - weight);
        blendedCurve.add(Point2D(x, y));
      }

      results.add(blendedCurve);
    }
    
    return results;
  }
  
  /// 生成发线方法 1 - 随机控制点
  static List<List<Point2D>> generateHairLines1(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];

    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];

    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 12 + (RandomUtils.randomFromInterval(-2, 2)).round(); // Further reduced hair points
      final List<Point2D> hairLine = [];

      // Start point - expand coverage area significantly
      int indexStart = (RandomUtils.randomFromInterval(25, 150)).round(); // Further expanded coverage area
      final int startIndex = (faceContourCopy.length - indexStart) % faceContourCopy.length;
      final Point2D startPoint = faceContourCopy[startIndex];
      hairLine.add(Point2D(startPoint.x * 0.95, startPoint.y * 0.95)); // Scale down slightly

      // Add random control points with expanded scaling
      for (int j = 1; j < numHairPoints + 1; j++) {
        indexStart = (RandomUtils.randomFromInterval(25, 150)).round(); // Further expanded coverage area
        final int index = (faceContourCopy.length - indexStart) % faceContourCopy.length;
        final Point2D originalPoint = faceContourCopy[index];
        final double scaleFactor = 0.9 + (j / numHairPoints) * 0.6; // Expanded scaling from 0.9 to 1.5
        // Add slight downward bias to make hair fall more naturally
        final double yBias = j > numHairPoints * 0.3 ? (j / numHairPoints) * 0.18 : 0;
        hairLine.add(Point2D(
          originalPoint.x * scaleFactor,
          originalPoint.y * scaleFactor + yBias * 10, // Increased downward bias for natural fall
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      final List<Point2D> curve = BezierCurveUtils.computeBezierCurve(hairLine, curvePoints);
      results.add(curve);
    }
    
    return results;
  }
  
  /// 生成发线方法 2 - 缩放和连接的曲线
  static List<List<Point2D>> generateHairLines2(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];

    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];

    // Pick and sort indices - expand coverage area significantly
    final List<int> pickedIndices = [];
    for (int i = 0; i < numHairLines; i++) {
      // Further expand hair coverage area for maximum volume
      pickedIndices.add((RandomUtils.randomFromInterval(20, 160)).round());
    }
    pickedIndices.sort();

    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 12 + (RandomUtils.randomFromInterval(-2, 2)).round(); // Further reduced hair points
      final List<Point2D> hairLine = [];
      final int indexOffset = pickedIndices[i];
      // Increase hair length range for more volume
      final double lower = RandomUtils.randomFromInterval(0.85, 1.3); // Expanded scaling range
      final int reverse = Random().nextBool() ? 1 : -1;

      for (int j = 0; j < numHairPoints; j++) {
        final double powerScale = RandomUtils.randomFromInterval(0.3, 1.5); // More varied scaling
        final double portion = (1 - pow(j / numHairPoints, powerScale)) * (1 - lower) + lower;
        // Expand portion range for larger hair coverage
        final double clampedPortion = portion.clamp(0.7, 1.6);
        final int index = (faceContourCopy.length - (reverse * j + indexOffset)) % faceContourCopy.length;

        // Add slight downward bias to make hair fall more naturally
        final double yBias = j > numHairPoints * 0.3 ? (j / numHairPoints) * 0.15 : 0;
        hairLine.add(Point2D(
          faceContourCopy[index].x * clampedPortion,
          faceContourCopy[index].y * clampedPortion + yBias * 8, // Increased downward bias for natural fall
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      List<Point2D> curve = BezierCurveUtils.computeBezierCurve(hairLine, curvePoints);
      if (Random().nextDouble() > 0.7) {
        curve = curve.reversed.toList();
      }

      if (results.isEmpty) {
        results.add(curve);
        continue;
      }

      // Check if we should connect to the last hair line
      final Point2D lastHairPoint = results.last.last;
      final double distance = sqrt(
        pow(curve.first.x - lastHairPoint.x, 2) + pow(curve.first.y - lastHairPoint.y, 2)
      );

      if (Random().nextDouble() > 0.5 && distance < 50) { // Reduced connection distance
        results.last.addAll(curve);
      } else {
        results.add(curve);
      }
    }

    return results;
  }
  
  /// 生成发线方法 3 - 分割和定向曲线
  static List<List<Point2D>> generateHairLines3(List<Point2D> faceContour, {int numHairLines = 100}) {
    if (faceContour.length < 3) return [];

    final List<Point2D> faceContourCopy = faceContour.sublist(0, faceContour.length - 2);
    final List<List<Point2D>> results = [];

    // Pick and sort indices - expand coverage area significantly
    final List<int> pickedIndices = [];
    for (int i = 0; i < numHairLines; i++) {
      // Further expand hair coverage area for maximum volume
      pickedIndices.add((RandomUtils.randomFromInterval(20, 160)).round());
    }
    pickedIndices.sort();

    final int splitPoint = (RandomUtils.randomFromInterval(70, 110)).round(); // More centered split

    for (int i = 0; i < numHairLines; i++) {
      final int numHairPoints = 15 + (RandomUtils.randomFromInterval(-3, 3)).round(); // Further reduced points
      final List<Point2D> hairLine = [];
      final int indexOffset = pickedIndices[i];

      // Expand hair length scaling for more volume
      double lower = RandomUtils.randomFromInterval(0.8, 1.4);
      if (Random().nextDouble() > 0.9) {
        lower = RandomUtils.randomFromInterval(0.75, 1.2);
      }

      final int reverse = indexOffset > splitPoint ? 1 : -1;

      for (int j = 0; j < numHairPoints; j++) {
        final double powerScale = RandomUtils.randomFromInterval(0.3, 1.6); // More varied scaling
        final double portion = (1 - pow(j / numHairPoints, powerScale)) * (1 - lower) + lower;
        // Expand portion range for larger hair coverage
        final double clampedPortion = portion.clamp(0.65, 1.7);
        final int index = (faceContourCopy.length - (reverse * j + indexOffset)) % faceContourCopy.length;

        // Add slight downward bias to make hair fall more naturally
        final double yBias = j > numHairPoints * 0.3 ? (j / numHairPoints) * 0.18 : 0;
        hairLine.add(Point2D(
          faceContourCopy[index].x * clampedPortion,
          faceContourCopy[index].y * (clampedPortion * 0.9) + yBias * 8, // Less Y compression and more downward bias
        ));
      }

      // Increase point density for smoother curves
      final int curvePoints = numHairPoints * 2; // Double the points for smoother curves
      final List<Point2D> curve = BezierCurveUtils.computeBezierCurve(hairLine, curvePoints);
      results.add(curve);
    }

    return results;
  }
  
  /// 使用多种方法组合生成随机头发（类似于 Vue 组件）
  static List<List<Point2D>> generateRandomHair(List<Point2D> faceContour) {
    final List<List<Point2D>> allHairLines = [];
    // 增加发线数量以增加体积
    final List<int> numHairLines = List.generate(4, (index) =>
        (RandomUtils.randomFromInterval(15, 45)).round()); // Increased from 5-25 to 15-45

    // 增加概率和发线数量以增加体积
    if (Random().nextDouble() > 0.2) { // Increased probability from 0.4 to 0.2
      allHairLines.addAll(generateHairLines0(faceContour, numHairLines: numHairLines[0] + 10)); // Increased base
    }

    if (Random().nextDouble() > 0.2) { // Increased probability from 0.4 to 0.2
      allHairLines.addAll(generateHairLines1(faceContour, numHairLines: numHairLines[1] + 10)); // Increased base
    }

    if (Random().nextDouble() > 0.3) { // Increased probability from 0.6 to 0.3
      allHairLines.addAll(generateHairLines2(faceContour, numHairLines: numHairLines[2] * 2 + 10)); // Increased base
    }

    if (Random().nextDouble() > 0.3) { // Increased probability from 0.6 to 0.3
      allHairLines.addAll(generateHairLines3(faceContour, numHairLines: numHairLines[3] * 2 + 10)); // Increased base
    }

    return allHairLines;
  }
}
